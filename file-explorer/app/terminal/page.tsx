"use client"

import React from 'react';
import { ClientSettingsWrapper } from '@/components/settings/client-settings-wrapper';
import TerminalHeader from '@/components/terminal/TerminalHeader';
import TerminalPanel from '@/components/terminal/TerminalPanel';

export default function TerminalPage() {
  return (
    <ClientSettingsWrapper>
      <div className="h-screen w-full bg-background flex flex-col">
        <TerminalHeader onDetach={() => {}} />
        <div className="flex-1 overflow-hidden">
          <TerminalPanel className="h-full" />
        </div>
      </div>
    </ClientSettingsWrapper>
  );
}
