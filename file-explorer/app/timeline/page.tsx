"use client"

import { useEffect, useState } from "react"
import dynamic from "next/dynamic"
import { Button } from "@/components/ui/button"
import { Minimize2, X } from "lucide-react"

// Dynamically import TaskTimelineInspector to avoid hydration issues
const TaskTimelineInspector = dynamic(
  () => import("@/components/inspector/TaskTimelineInspector"),
  {
    ssr: false,
    loading: () => (
      <div className="h-full flex items-center justify-center">
        <div className="text-muted-foreground">Loading Timeline Inspector...</div>
      </div>
    )
  }
)

export default function TimelineWindowPage() {
  const [isMounted, setIsMounted] = useState(false)

  const handleClose = () => {
    // Close the floating window
    if (typeof window !== 'undefined' && window.close) {
      window.close();
    } else {
      console.warn('Cannot close window - window.close not available');
    }
  };

  const handleMinimize = () => {
    // Minimize the floating window - not available in web context
    console.log('Minimize requested');
  };

  // Handle mounting and keyboard shortcuts
  useEffect(() => {
    setIsMounted(true)

    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape to close window
      if (e.key === "Escape") {
        e.preventDefault()
        handleClose()
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  if (!isMounted) {
    return (
      <div className="h-screen flex items-center justify-center bg-background text-foreground">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col bg-background text-foreground">
      {/* Window controls */}
      <div className="h-8 bg-muted/50 border-b border-border flex items-center justify-between px-3 drag-region">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 rounded-full bg-red-500"></div>
          <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
          <div className="w-2 h-2 rounded-full bg-green-500"></div>
        </div>
        <div className="text-xs font-medium text-muted-foreground">
          Task Timeline Inspector
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground no-drag"
            onClick={handleMinimize}
          >
            <Minimize2 className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground hover:bg-red-500/20 no-drag"
            onClick={handleClose}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Timeline Inspector Content */}
      <div className="flex-1 overflow-hidden">
        <TaskTimelineInspector />
      </div>
    </div>
  )
}
