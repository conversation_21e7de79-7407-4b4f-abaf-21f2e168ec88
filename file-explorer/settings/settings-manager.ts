// settings/settings-manager.ts
// Settings management service for Electron application

export interface RecentProject {
  name: string;
  path: string;
  lastOpened: number;
}

export interface AppSettings {
  recentProjects: RecentProject[];
  maxRecentProjects: number;
  theme: 'light' | 'dark' | 'system';
  autoSave: boolean;
  autoSaveInterval: number;
  editorSettings: {
    fontSize: number;
    fontFamily: string;
    tabSize: number;
    wordWrap: boolean;
    minimap: boolean;
  };
}

class SettingsManager {
  private settings: AppSettings = {
    recentProjects: [],
    maxRecentProjects: 10,
    theme: 'system',
    autoSave: true,
    autoSaveInterval: 30000,
    editorSettings: {
      fontSize: 14,
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
      tabSize: 2,
      wordWrap: true,
      minimap: true
    }
  };

  private settingsPath = 'app-settings.json';

  /**
   * Initialize settings manager
   */
  async initialize(): Promise<void> {
    try {
      await this.loadSettings();
      console.log('✅ Settings manager initialized');
    } catch (error) {
      console.warn('⚠️ Failed to initialize settings manager:', error);
    }
  }

  /**
   * Load settings from storage
   */
  private async loadSettings(): Promise<void> {
    try {
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        const result = await (window as any).electronAPI.readFile(this.settingsPath);
        if (result.success) {
          const loadedSettings = JSON.parse(result.content);
          this.settings = { ...this.settings, ...loadedSettings };
        }
      } else {
        // Fallback to localStorage in web environment
        const stored = localStorage.getItem('app-settings');
        if (stored) {
          const loadedSettings = JSON.parse(stored);
          this.settings = { ...this.settings, ...loadedSettings };
        }
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }
  }

  /**
   * Save settings to storage
   */
  private async saveSettings(): Promise<void> {
    try {
      const settingsJson = JSON.stringify(this.settings, null, 2);
      
      if (typeof window !== 'undefined' && (window as any).electronAPI) {
        await (window as any).electronAPI.saveFile(this.settingsPath, settingsJson);
      } else {
        // Fallback to localStorage in web environment
        localStorage.setItem('app-settings', settingsJson);
      }
    } catch (error) {
      console.warn('Failed to save settings:', error);
    }
  }

  /**
   * Get recent projects list
   */
  async getRecentProjects(): Promise<RecentProject[]> {
    return this.settings.recentProjects;
  }

  /**
   * Add a project to recent projects
   */
  async addRecentProject(name: string, path: string): Promise<void> {
    const existingIndex = this.settings.recentProjects.findIndex(p => p.path === path);
    
    const project: RecentProject = {
      name,
      path,
      lastOpened: Date.now()
    };

    if (existingIndex >= 0) {
      // Update existing project
      this.settings.recentProjects[existingIndex] = project;
    } else {
      // Add new project
      this.settings.recentProjects.unshift(project);
    }

    // Keep only the most recent projects
    this.settings.recentProjects = this.settings.recentProjects
      .sort((a, b) => b.lastOpened - a.lastOpened)
      .slice(0, this.settings.maxRecentProjects);

    await this.saveSettings();
  }

  /**
   * Remove a project from recent projects
   */
  async removeRecentProject(path: string): Promise<void> {
    this.settings.recentProjects = this.settings.recentProjects.filter(p => p.path !== path);
    await this.saveSettings();
  }

  /**
   * Create a new project entry
   */
  async createProject(name: string, path: string): Promise<void> {
    await this.addRecentProject(name, path);
  }

  /**
   * Get app settings
   */
  getSettings(): AppSettings {
    return { ...this.settings };
  }

  /**
   * Update app settings
   */
  async updateSettings(updates: Partial<AppSettings>): Promise<void> {
    this.settings = { ...this.settings, ...updates };
    await this.saveSettings();
  }

  /**
   * Get editor settings
   */
  getEditorSettings() {
    return { ...this.settings.editorSettings };
  }

  /**
   * Update editor settings
   */
  async updateEditorSettings(updates: Partial<AppSettings['editorSettings']>): Promise<void> {
    this.settings.editorSettings = { ...this.settings.editorSettings, ...updates };
    await this.saveSettings();
  }

  /**
   * Get theme setting
   */
  getTheme(): 'light' | 'dark' | 'system' {
    return this.settings.theme;
  }

  /**
   * Set theme setting
   */
  async setTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    this.settings.theme = theme;
    await this.saveSettings();
  }

  /**
   * Reset settings to defaults
   */
  async resetSettings(): Promise<void> {
    this.settings = {
      recentProjects: [],
      maxRecentProjects: 10,
      theme: 'system',
      autoSave: true,
      autoSaveInterval: 30000,
      editorSettings: {
        fontSize: 14,
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
        tabSize: 2,
        wordWrap: true,
        minimap: true
      }
    };
    await this.saveSettings();
  }
}

// Export singleton instance
export const settingsManager = new SettingsManager();
