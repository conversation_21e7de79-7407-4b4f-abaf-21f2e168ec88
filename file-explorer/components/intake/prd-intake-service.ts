// components/intake/prd-intake-service.ts
import { activeProjectService } from '../../services/active-project-service';
import { taskmasterIntegrationService } from '../services/taskmaster-integration-service';
import { settingsManager } from '../settings/settings-manager';

export interface PRDValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  score: number; // 0-100 quality score
  sections: {
    hasObjective: boolean;
    hasRequirements: boolean;
    hasAcceptanceCriteria: boolean;
    hasUserStories: boolean;
    hasTechnicalSpecs: boolean;
  };
}

export interface PRDParseResult {
  success: boolean;
  tasksFilePath?: string;
  taskCount?: number;
  error?: string;
  output?: string;
}

export interface PRDUploadResult {
  success: boolean;
  filePath?: string;
  validation?: PRDValidationResult;
  error?: string;
}

export class PRDIntakeService {
  private static instance: PRDIntakeService;
  private currentPRDPath: string | null = null;
  private validationResult: PRDValidationResult | null = null;

  private constructor() {}

  public static getInstance(): PRDIntakeService {
    if (!PRDIntakeService.instance) {
      PRDIntakeService.instance = new PRDIntakeService();
    }
    return PRDIntakeService.instance;
  }

  /**
   * ✅ Validate PRD content for minimum quality requirements
   */
  public validatePRDContent(content: string): PRDValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let score = 0;

    // Check minimum length
    if (content.length < 500) {
      errors.push('PRD is too short (minimum 500 characters required)');
    } else {
      score += 10;
    }

    // Check for essential sections
    const sections = {
      hasObjective: this.hasSection(content, ['objective', 'goal', 'purpose', 'overview']),
      hasRequirements: this.hasSection(content, ['requirements', 'features', 'functionality']),
      hasAcceptanceCriteria: this.hasSection(content, ['acceptance', 'criteria', 'definition of done']),
      hasUserStories: this.hasSection(content, ['user story', 'user stories', 'scenarios']),
      hasTechnicalSpecs: this.hasSection(content, ['technical', 'architecture', 'implementation'])
    };

    // Score based on sections present
    if (sections.hasObjective) score += 20;
    else errors.push('Missing project objective/goal section');

    if (sections.hasRequirements) score += 25;
    else errors.push('Missing requirements/features section');

    if (sections.hasAcceptanceCriteria) score += 20;
    else warnings.push('Missing acceptance criteria (recommended)');

    if (sections.hasUserStories) score += 15;
    else warnings.push('Missing user stories (recommended)');

    if (sections.hasTechnicalSpecs) score += 10;
    else warnings.push('Missing technical specifications (recommended)');

    // Check for actionable content
    const actionWords = ['create', 'build', 'implement', 'develop', 'design', 'add', 'remove', 'update', 'modify'];
    const hasActionableContent = actionWords.some(word => 
      content.toLowerCase().includes(word)
    );

    if (hasActionableContent) {
      score += 10;
    } else {
      warnings.push('PRD lacks actionable development tasks');
    }

    const isValid = errors.length === 0 && score >= 50;

    return {
      isValid,
      errors,
      warnings,
      score: Math.min(score, 100),
      sections
    };
  }

  /**
   * ✅ Check if content contains specific section keywords
   */
  private hasSection(content: string, keywords: string[]): boolean {
    const lowerContent = content.toLowerCase();
    return keywords.some(keyword => {
      // Look for keyword as header (with #, ##, etc.) or as standalone line
      const headerPattern = new RegExp(`^#+\\s*.*${keyword}.*$`, 'mi');
      const linePattern = new RegExp(`^.*${keyword}.*:?$`, 'mi');
      return headerPattern.test(lowerContent) || linePattern.test(lowerContent);
    });
  }

  /**
   * ✅ Upload and save PRD to project scripts directory
   */
  public async uploadPRD(content: string, projectPath?: string): Promise<PRDUploadResult> {
    try {
      // Get active project path if not provided
      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;
      
      if (!targetProjectPath) {
        return {
          success: false,
          error: 'No active project selected. Please create or open a project first.'
        };
      }

      // Validate PRD content
      const validation = this.validatePRDContent(content);
      
      if (!validation.isValid) {
        return {
          success: false,
          error: `PRD validation failed: ${validation.errors.join(', ')}`,
          validation
        };
      }

      // Create scripts directory if it doesn't exist
      const scriptsDir = `${targetProjectPath}/scripts`;
      const prdFilePath = `${scriptsDir}/prd.txt`;

      // Use Electron API to create directory and file
      if (typeof window !== 'undefined' && window.electronAPI) {
        // Create scripts directory
        await window.electronAPI.createFile(`${scriptsDir}/.gitkeep`, '');
        
        // Save PRD content
        const saveResult = await window.electronAPI.saveFile(prdFilePath, content);
        
        if (!saveResult.success) {
          return {
            success: false,
            error: `Failed to save PRD: ${saveResult.error}`
          };
        }

        // Store validation result and path
        this.currentPRDPath = prdFilePath;
        this.validationResult = validation;

        console.log(`✅ PRD saved to: ${prdFilePath}`);
        console.log(`📊 PRD Quality Score: ${validation.score}/100`);

        return {
          success: true,
          filePath: prdFilePath,
          validation
        };
      } else {
        return {
          success: false,
          error: 'File system operations not available (Electron API required)'
        };
      }
    } catch (error) {
      console.error('PRD upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * ✅ Parse PRD using Taskmaster CLI
   */
  public async parsePRDWithTaskmaster(projectPath?: string): Promise<PRDParseResult> {
    try {
      // ✅ Step 1: Get active project context (prioritize passed path, then active project)
      let targetProjectPath = projectPath;
      let projectName = 'Unknown Project';

      if (!targetProjectPath) {
        const activeProject = activeProjectService.getActiveProject();
        if (!activeProject?.path) {
          return {
            success: false,
            error: 'No active project selected. Please select or create a project before parsing PRD.'
          };
        }
        targetProjectPath = activeProject.path;
        projectName = activeProject.name;
      }

      console.log(`🔧 PRD Parsing: Using project context - ${projectName} (${targetProjectPath})`);

      // ✅ Step 2: Validate API keys are configured
      const hasAnthropicKey = !!settingsManager.getApiKey('anthropic');
      const hasOpenAIKey = !!settingsManager.getApiKey('openai');
      const hasOpenRouterKey = !!settingsManager.getApiKey('openrouter');

      if (!hasAnthropicKey && !hasOpenAIKey && !hasOpenRouterKey) {
        return {
          success: false,
          error: 'No AI API keys configured. Please configure at least one API key (Anthropic, OpenAI, or OpenRouter) in Settings before parsing PRD.'
        };
      }

      // ✅ Step 3: Ensure Taskmaster is initialized for this specific project
      console.log(`🔧 PRD Parsing: Initializing Taskmaster for project: ${targetProjectPath}`);
      const initResult = await taskmasterIntegrationService.ensureInitialized();
      if (!initResult.success) {
        return {
          success: false,
          error: `Taskmaster initialization failed: ${initResult.error}`
        };
      }

      // ✅ Verify the initialization used the correct project path
      if (initResult.projectPath && initResult.projectPath !== targetProjectPath) {
        console.warn(`⚠️ Taskmaster initialized with different path: expected ${targetProjectPath}, got ${initResult.projectPath}`);
      }

      const prdPath = `${targetProjectPath}/scripts/prd.txt`;

      // ✅ Step 4: Check if PRD exists
      if (typeof window !== 'undefined' && window.electronAPI) {
        const prdExists = await window.electronAPI.readFile(prdPath);
        if (!prdExists.success) {
          return {
            success: false,
            error: 'PRD file not found. Please upload a PRD first.'
          };
        }

        // ✅ Step 5: Execute task-master CLI command with enhanced error handling
        console.log(`📁 Taskmaster Execution Path:`, targetProjectPath);
        console.log(`📄 PRD File Path:`, prdPath);

        // ✅ Validate targetProjectPath is not null/undefined
        if (!targetProjectPath || typeof targetProjectPath !== 'string') {
          return {
            success: false,
            error: `Invalid project path: ${targetProjectPath}. Cannot execute Taskmaster without a valid working directory.`
          };
        }

        console.log(`🔧 Executing Taskmaster parse-prd for: ${prdPath}`);
        const command = `npx task-master parse-prd "${prdPath}"`;
        const result = await window.electronAPI.executeCommand(command, targetProjectPath);

        console.log(`📊 Taskmaster command result:`, {
          success: result.success,
          error: result.error,
          output: result.output?.substring(0, 500) // Log first 500 chars
        });

        if (result.success) {
          const tasksFilePath = `${targetProjectPath}/.taskmaster/tasks.json`;
          
          // Verify tasks.json was created
          const tasksFile = await window.electronAPI.readFile(tasksFilePath);
          if (tasksFile.success) {
            try {
              const tasks = JSON.parse(tasksFile.content);
              const taskCount = Array.isArray(tasks) ? tasks.length : Object.keys(tasks).length;
              
              console.log(`✅ Taskmaster parsed PRD successfully: ${taskCount} tasks generated`);
              
              return {
                success: true,
                tasksFilePath,
                taskCount,
                output: result.output
              };
            } catch (parseError) {
              return {
                success: false,
                error: 'Generated tasks.json is not valid JSON',
                output: result.output
              };
            }
          } else {
            return {
              success: false,
              error: 'Taskmaster completed but tasks.json was not generated',
              output: result.output
            };
          }
        } else {
          // ✅ Enhanced error message parsing
          let errorMessage = result.error || 'Unknown error occurred';

          // Check for specific error patterns
          if (result.output?.includes('AI service call failed')) {
            errorMessage = 'AI service configuration error. Please check your API keys in Settings and ensure they are valid.';
          } else if (result.output?.includes('ENOENT') || result.output?.includes('command not found')) {
            errorMessage = 'Taskmaster CLI not found. Please install task-master: npm install -g task-master';
          } else if (result.output?.includes('authentication') || result.output?.includes('unauthorized')) {
            errorMessage = 'API authentication failed. Please verify your API keys are correct and have sufficient permissions.';
          } else if (result.output?.includes('rate limit') || result.output?.includes('quota')) {
            errorMessage = 'API rate limit exceeded. Please wait a moment and try again.';
          }

          return {
            success: false,
            error: `Taskmaster CLI failed: ${errorMessage}`,
            output: result.output
          };
        }
      } else {
        return {
          success: false,
          error: 'Command execution not available (Electron API required)'
        };
      }
    } catch (error) {
      console.error('PRD parsing failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * ✅ Check if current project has valid PRD
   */
  public async hasValidPRD(projectPath?: string): Promise<boolean> {
    try {
      const targetProjectPath = projectPath || activeProjectService.getActiveProject()?.path;
      
      if (!targetProjectPath) {
        return false;
      }

      const prdPath = `${targetProjectPath}/scripts/prd.txt`;
      
      if (typeof window !== 'undefined' && window.electronAPI) {
        const prdFile = await window.electronAPI.readFile(prdPath);
        if (prdFile.success) {
          const validation = this.validatePRDContent(prdFile.content);
          return validation.isValid;
        }
      }
      
      return false;
    } catch (error) {
      console.error('PRD validation check failed:', error);
      return false;
    }
  }

  /**
   * ✅ Get current PRD validation status
   */
  public getCurrentValidation(): PRDValidationResult | null {
    return this.validationResult;
  }

  /**
   * ✅ Get current PRD file path
   */
  public getCurrentPRDPath(): string | null {
    return this.currentPRDPath;
  }

  /**
   * ✅ Clear current PRD state
   */
  public clearPRDState(): void {
    this.currentPRDPath = null;
    this.validationResult = null;
  }
}

// Export singleton instance
export const prdIntakeService = PRDIntakeService.getInstance();
