// components/settings/settings-manager.ts
import { getConfigStoreBrowser, ConfigStoreBrowser } from '../background/config-store-browser';

export interface AgentSettings {
  id: string;
  name: string;
  enabled: boolean;
  model: string;
  provider: 'openai' | 'anthropic' | 'openrouter' | 'azure' | 'google' | 'deepseek' | 'fireworks' | 'mcp';
  maxTokens: number;
  temperature: number;
  customPrompt?: string;
  capabilities: string[];
  // ✅ MCP Protocol Support
  useMCP?: boolean;
  mcpConfig?: {
    serverId: string;
    serverCommand?: string;
    serverArgs?: string[];
    fallbackToLLM?: boolean;
  };
}

export interface SystemSettings {
  theme: 'light' | 'dark' | 'system';
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  maxConcurrentTasks: number;
  defaultTimeout: number; // in milliseconds
  enableTelemetry: boolean;
  debugMode: boolean;
  testModeEnabled: boolean; // Enable stress testing and development features
}

export interface CostSettings {
  budgetLimit: number; // monthly budget in USD
  alertThreshold: number; // percentage of budget
  trackUsage: boolean;
  showCostEstimates: boolean;
  preferCheaperModels: boolean;
}

export interface PrivacySettings {
  shareUsageData: boolean;
  localOnly: boolean;
  encryptPrompts: boolean;
  clearHistoryOnExit: boolean;
  maxHistoryDays: number;
}

export interface EditorSettings {
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: boolean;
  lineNumbers: boolean;
  minimap: boolean;
  autoFormat: boolean;
  autoComplete: boolean;
}

export interface TerminalSettings {
  theme: 'dark' | 'light' | 'system';
  fontFamily: string;
  fontSize: number;
  shell: string;
  cols: number;
  rows: number;
  scrollback: number;
  cursorBlink: boolean;
  lineHeight: number;
}

export interface MCPSettings {
  enabled: boolean;
  servers: Record<string, {
    name: string;
    command: string;
    args: string[];
    enabled: boolean;
    autoConnect: boolean;
  }>;
  defaultServer?: string;
  timeout: number;
  maxRetries: number;
}

export interface TaskmasterSettings {
  taskmasterBinaryPath: string;
  defaultParseCommand: string;
  defaultModel?: string; // Optional override for inherited agent model
  autoExpandTasks: boolean;
  enableVerboseLogs: boolean;
  maxTasks: number;
  complexityThreshold: number;
}

export interface AllSettings {
  system: SystemSettings;
  agents: AgentSettings[];
  cost: CostSettings;
  privacy: PrivacySettings;
  editor: EditorSettings;
  terminal: TerminalSettings;
  apiKeys: Record<string, string>;
  mcp: MCPSettings;
  taskmaster: TaskmasterSettings;
}

export class SettingsManager {
  private settings: AllSettings;
  private listeners: ((settings: AllSettings) => void)[] = [];
  private configStore: ConfigStoreBrowser;
  private initialized = false;

  constructor() {
    this.settings = this.getDefaultSettings();
    this.configStore = getConfigStoreBrowser();
    this.loadSettings();
  }

  private getDefaultSettings(): AllSettings {
    return {
      system: {
        theme: 'system',
        autoSave: true,
        autoSaveInterval: 30,
        maxConcurrentTasks: 5,
        defaultTimeout: 30000,
        enableTelemetry: false,
        debugMode: false,
        testModeEnabled: false
      },
      agents: [
        {
          id: 'micromanager',
          name: 'Micromanager',
          enabled: true,
          model: 'claude-3-sonnet',
          provider: 'anthropic',
          maxTokens: 8000,
          temperature: 0.7,
          capabilities: ['task_orchestration', 'agent_coordination']
        },
        {
          id: 'intern',
          name: 'Intern',
          enabled: true,
          model: 'gpt-3.5-turbo',
          provider: 'openai',
          maxTokens: 2000,
          temperature: 0.3,
          capabilities: ['simple_tasks', 'boilerplate_generation']
        },
        {
          id: 'junior',
          name: 'Junior',
          enabled: true,
          model: 'gpt-3.5-turbo',
          provider: 'openai',
          maxTokens: 4000,
          temperature: 0.5,
          capabilities: ['single_file_implementation', 'moderate_complexity']
        },
        {
          id: 'midlevel',
          name: 'MidLevel',
          enabled: true,
          model: 'claude-3-sonnet',
          provider: 'anthropic',
          maxTokens: 6000,
          temperature: 0.6,
          capabilities: ['multi_file_implementation', 'component_integration']
        },
        {
          id: 'senior',
          name: 'Senior',
          enabled: true,
          model: 'gpt-4',
          provider: 'openai',
          maxTokens: 8000,
          temperature: 0.7,
          capabilities: ['complex_system_implementation', 'architectural_decisions']
        },
        {
          id: 'researcher',
          name: 'Researcher',
          enabled: true,
          model: 'claude-3-sonnet',
          provider: 'anthropic',
          maxTokens: 6000,
          temperature: 0.4,
          capabilities: ['codebase_analysis', 'pattern_recognition']
        },
        {
          id: 'architect',
          name: 'Architect',
          enabled: true,
          model: 'gpt-4',
          provider: 'openai',
          maxTokens: 8000,
          temperature: 0.6,
          capabilities: ['system_design', 'technical_strategy']
        },
        {
          id: 'designer',
          name: 'Designer',
          enabled: true,
          model: 'claude-3-sonnet',
          provider: 'anthropic',
          maxTokens: 4000,
          temperature: 0.8,
          capabilities: ['ui_design', 'component_styling']
        },
        {
          id: 'tester',
          name: 'Tester',
          enabled: true,
          model: 'gpt-3.5-turbo',
          provider: 'openai',
          maxTokens: 4000,
          temperature: 0.4,
          capabilities: ['test_generation', 'quality_assurance']
        }
      ],
      cost: {
        budgetLimit: 100,
        alertThreshold: 80,
        trackUsage: true,
        showCostEstimates: true,
        preferCheaperModels: false
      },
      privacy: {
        shareUsageData: false,
        localOnly: true,
        encryptPrompts: true,
        clearHistoryOnExit: false,
        maxHistoryDays: 30
      },
      editor: {
        fontSize: 14,
        fontFamily: 'JetBrains Mono, Monaco, Consolas, monospace',
        tabSize: 2,
        wordWrap: true,
        lineNumbers: true,
        minimap: true,
        autoFormat: true,
        autoComplete: true
      },
      terminal: {
        theme: 'system',
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
        fontSize: 13,
        shell: process.platform === 'win32' ? 'powershell.exe' : 'bash',
        cols: 80,
        rows: 24,
        scrollback: 1000,
        cursorBlink: true,
        lineHeight: 1.2
      },
      apiKeys: {},
      mcp: {
        enabled: false,
        servers: {
          'claude-desktop': {
            name: 'Claude Desktop',
            command: 'claude-desktop',
            args: ['--mcp'],
            enabled: false,
            autoConnect: false
          },
          'roo-code': {
            name: 'Roo Code',
            command: 'roo',
            args: ['--mcp-server'],
            enabled: false,
            autoConnect: false
          },
          'cursor-ai': {
            name: 'Cursor AI',
            command: 'cursor',
            args: ['--mcp-mode'],
            enabled: false,
            autoConnect: false
          }
        },
        timeout: 30000,
        maxRetries: 3
      },
      taskmaster: {
        taskmasterBinaryPath: 'npx task-master',
        defaultParseCommand: 'npx task-master parse-prd',
        defaultModel: undefined, // Inherit from agent settings
        autoExpandTasks: false,
        enableVerboseLogs: false,
        maxTasks: 50,
        complexityThreshold: 5
      }
    };
  }

  private async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await this.configStore.initialize();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize config store:', error);
      // Fall back to localStorage
    }
  }

  private loadSettings(): void {
    this.loadSettingsAsync().catch(error => {
      console.error('Failed to load settings:', error);
    });
  }

  private async loadSettingsAsync(): Promise<void> {
    try {
      // Skip loading during SSR
      if (typeof window === 'undefined') {
        return;
      }

      await this.initialize();

      if (this.initialized) {
        // Load from persistent storage
        const systemSettings = await this.configStore.getGlobalSettingsByCategory('system');
        const agentSettings = await this.configStore.getGlobalSettingsByCategory('agents');
        const costSettings = await this.configStore.getGlobalSettingsByCategory('cost');
        const privacySettings = await this.configStore.getGlobalSettingsByCategory('privacy');
        const editorSettings = await this.configStore.getGlobalSettingsByCategory('editor');
        const terminalSettings = await this.configStore.getGlobalSettingsByCategory('terminal');
        const taskmasterSettings = await this.configStore.getGlobalSettingsByCategory('taskmaster');
        const apiKeys = await this.configStore.getGlobalSettingsByCategory('apiKeys');

        if (Object.keys(systemSettings).length > 0) {
          this.settings.system = { ...this.settings.system, ...systemSettings };
        }
        if (Object.keys(costSettings).length > 0) {
          this.settings.cost = { ...this.settings.cost, ...costSettings };
        }
        if (Object.keys(privacySettings).length > 0) {
          this.settings.privacy = { ...this.settings.privacy, ...privacySettings };
        }
        if (Object.keys(editorSettings).length > 0) {
          this.settings.editor = { ...this.settings.editor, ...editorSettings };
        }
        if (Object.keys(terminalSettings).length > 0) {
          this.settings.terminal = { ...this.settings.terminal, ...terminalSettings };
        }
        if (Object.keys(taskmasterSettings).length > 0) {
          this.settings.taskmaster = { ...this.settings.taskmaster, ...taskmasterSettings };
        }
        if (Object.keys(apiKeys).length > 0) {
          this.settings.apiKeys = { ...this.settings.apiKeys, ...apiKeys };
        }

        // Handle agent settings array
        if (agentSettings.agents) {
          this.settings.agents = agentSettings.agents;
        }
      } else {
        // Fall back to localStorage
        this.loadFromLocalStorage();
      }
    } catch (error) {
      console.error('Failed to load settings from config store:', error);
      this.loadFromLocalStorage();
    }
  }

  private loadFromLocalStorage(): void {
    try {
      // Check if localStorage is available (not available during SSR)
      if (typeof window !== 'undefined' && window.localStorage) {
        const stored = localStorage.getItem('synapse-settings');
        if (stored) {
          const parsed = JSON.parse(stored);
          this.settings = { ...this.settings, ...parsed };
        }
      }
    } catch (error) {
      console.error('Failed to load settings from localStorage:', error);
    }
  }

  public saveSettings(): void {
    this.saveSettingsAsync().catch(error => {
      console.error('Failed to save settings:', error);
    });
  }

  private async saveSettingsAsync(): Promise<void> {
    try {
      // Skip saving during SSR
      if (typeof window === 'undefined') {
        return;
      }

      await this.initialize();

      if (this.initialized) {
        // Save to persistent storage
        await this.saveToConfigStore();
      } else {
        // Fall back to localStorage
        this.saveToLocalStorage();
      }

      this.notifyListeners();
    } catch (error) {
      console.error('Failed to save settings to config store:', error);
      this.saveToLocalStorage();
      this.notifyListeners();
    }
  }

  private async saveToConfigStore(): Promise<void> {
    // Save system settings
    for (const [key, value] of Object.entries(this.settings.system)) {
      await this.configStore.setGlobalSetting('system', key, value);
    }

    // Save cost settings
    for (const [key, value] of Object.entries(this.settings.cost)) {
      await this.configStore.setGlobalSetting('cost', key, value);
    }

    // Save privacy settings
    for (const [key, value] of Object.entries(this.settings.privacy)) {
      await this.configStore.setGlobalSetting('privacy', key, value);
    }

    // Save editor settings
    for (const [key, value] of Object.entries(this.settings.editor)) {
      await this.configStore.setGlobalSetting('editor', key, value);
    }

    // Save terminal settings
    for (const [key, value] of Object.entries(this.settings.terminal)) {
      await this.configStore.setGlobalSetting('terminal', key, value);
    }

    // Save taskmaster settings
    for (const [key, value] of Object.entries(this.settings.taskmaster)) {
      await this.configStore.setGlobalSetting('taskmaster', key, value);
    }

    // Save agent settings as a single object
    await this.configStore.setGlobalSetting('agents', 'agents', this.settings.agents);

    // Save API keys (encrypted)
    for (const [key, value] of Object.entries(this.settings.apiKeys)) {
      await this.configStore.setGlobalSetting('apiKeys', key, value, true);
    }
  }

  private saveToLocalStorage(): void {
    try {
      // Check if localStorage is available (not available during SSR)
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem('synapse-settings', JSON.stringify(this.settings));
      }
    } catch (error) {
      console.error('Failed to save settings to localStorage:', error);
    }
  }

  public getSettings(): AllSettings {
    return { ...this.settings };
  }

  public updateSystemSettings(updates: Partial<SystemSettings>): void {
    this.settings.system = { ...this.settings.system, ...updates };
    this.saveSettings();
  }

  public updateAgentSettings(agentId: string, updates: Partial<AgentSettings>): void {
    const agentIndex = this.settings.agents.findIndex(a => a.id === agentId);
    if (agentIndex !== -1) {
      this.settings.agents[agentIndex] = { ...this.settings.agents[agentIndex], ...updates };
      this.saveSettings();
    }
  }

  public updateCostSettings(updates: Partial<CostSettings>): void {
    this.settings.cost = { ...this.settings.cost, ...updates };
    this.saveSettings();
  }

  public updatePrivacySettings(updates: Partial<PrivacySettings>): void {
    this.settings.privacy = { ...this.settings.privacy, ...updates };
    this.saveSettings();
  }

  public updateEditorSettings(updates: Partial<EditorSettings>): void {
    this.settings.editor = { ...this.settings.editor, ...updates };
    this.saveSettings();
  }

  public updateTerminalSettings(updates: Partial<TerminalSettings>): void {
    this.settings.terminal = { ...this.settings.terminal, ...updates };
    this.saveSettings();
  }

  public updateTaskmasterSettings(updates: Partial<TaskmasterSettings>): void {
    this.settings.taskmaster = { ...this.settings.taskmaster, ...updates };
    this.saveSettings();
  }

  public getTaskmasterSettings(): TaskmasterSettings {
    return { ...this.settings.taskmaster };
  }

  public setApiKey(provider: string, key: string): void {
    this.settings.apiKeys[provider] = key;
    this.saveSettings();
  }

  public getApiKey(provider: string): string | undefined {
    return this.settings.apiKeys[provider];
  }

  public removeApiKey(provider: string): void {
    delete this.settings.apiKeys[provider];
    this.saveSettings();
  }

  // ✅ MCP Settings Management
  public updateMCPSettings(updates: Partial<MCPSettings>): void {
    this.settings.mcp = { ...this.settings.mcp, ...updates };
    this.saveSettings();
  }

  public getMCPSettings(): MCPSettings {
    return { ...this.settings.mcp };
  }

  public updateMCPServer(serverId: string, updates: Partial<MCPSettings['servers'][string]>): void {
    if (!this.settings.mcp.servers[serverId]) {
      this.settings.mcp.servers[serverId] = {
        name: serverId,
        command: '',
        args: [],
        enabled: false,
        autoConnect: false
      };
    }
    this.settings.mcp.servers[serverId] = { ...this.settings.mcp.servers[serverId], ...updates };
    this.saveSettings();
  }

  public removeMCPServer(serverId: string): void {
    delete this.settings.mcp.servers[serverId];
    this.saveSettings();
  }

  public onSettingsChange(listener: (settings: AllSettings) => void): void {
    this.listeners.push(listener);
  }

  public offSettingsChange(listener: (settings: AllSettings) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.settings));
  }

  // ✅ Get current system settings (for theme bridge)
  public getSystemSettings(): SystemSettings {
    return { ...this.settings.system };
  }

  /**
   * Get a complete snapshot of all settings for export
   */
  public getFullSettingsSnapshot(): AllSettings {
    return { ...this.settings };
  }

  /**
   * Export settings as JSON string (legacy method for backward compatibility)
   */
  public exportSettings(): string {
    // Remove sensitive data before export
    const exportData = { ...this.settings };
    exportData.apiKeys = {}; // Don't export API keys

    // Add metadata
    const exportWithMetadata = {
      ...exportData,
      _metadata: {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'Synapse Agent System'
      }
    };

    return JSON.stringify(exportWithMetadata, null, 2);
  }

  /**
   * Export settings as a downloadable blob
   */
  public exportSettingsAsBlob(): Blob {
    const { exportSettings } = require('../../lib/io/settings-exporter');
    return exportSettings(this.settings, { includeApiKeys: false });
  }

  /**
   * Apply a validated settings snapshot
   */
  public applySettingsSnapshot(settings: Partial<AllSettings>): void {
    // Preserve API keys (never overwrite them from import)
    const currentApiKeys = this.settings.apiKeys;

    // Apply imported settings
    if (settings.system) {
      this.settings.system = { ...this.settings.system, ...settings.system };
    }

    if (settings.agents) {
      this.settings.agents = settings.agents;
    }

    if (settings.cost) {
      this.settings.cost = { ...this.settings.cost, ...settings.cost };
    }

    if (settings.privacy) {
      this.settings.privacy = { ...this.settings.privacy, ...settings.privacy };
    }

    if (settings.editor) {
      this.settings.editor = { ...this.settings.editor, ...settings.editor };
    }

    if (settings.terminal) {
      this.settings.terminal = { ...this.settings.terminal, ...settings.terminal };
    }

    if (settings.taskmaster) {
      this.settings.taskmaster = { ...this.settings.taskmaster, ...settings.taskmaster };
    }

    // Always preserve current API keys
    this.settings.apiKeys = currentApiKeys;

    this.saveSettings();
    this.notifyListeners();
  }

  /**
   * Import settings with validation (legacy method for backward compatibility)
   */
  public importSettings(settingsJson: string): boolean {
    try {
      const { importSettings } = require('../../lib/io/settings-exporter');
      const result = importSettings(settingsJson);

      if (!result.success) {
        console.error('Failed to import settings:', result.error);
        return false;
      }

      if (result.settings) {
        this.applySettingsSnapshot(result.settings);
      }

      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }

  public resetToDefaults(): void {
    const apiKeys = this.settings.apiKeys; // Preserve API keys
    this.settings = this.getDefaultSettings();
    this.settings.apiKeys = apiKeys;
    this.saveSettings();
  }

  // Project Management Methods
  public async createProject(name: string, path: string, template?: string): Promise<string> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    const { createDefaultProjectConfig } = await import('../background/default-configs');
    const projectConfig = createDefaultProjectConfig(path, template as any);
    projectConfig.name = name;

    const created = await this.configStore.createProject(projectConfig);
    return created.id;
  }

  public async getProject(id: string) {
    await this.initialize();

    if (!this.initialized) {
      return null;
    }

    return await this.configStore.getProject(id);
  }

  public async getProjectByPath(path: string) {
    await this.initialize();

    if (!this.initialized) {
      return null;
    }

    return await this.configStore.getProjectByPath(path);
  }

  public async getAllProjects() {
    await this.initialize();

    if (!this.initialized) {
      return [];
    }

    return await this.configStore.getAllProjects();
  }

  public async updateProject(id: string, updates: any) {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    return await this.configStore.updateProject(id, updates);
  }

  public async deleteProject(id: string): Promise<boolean> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    return await this.configStore.deleteProject(id);
  }

  // Database management (via IPC in Electron)
  public async backupDatabase(backupPath: string): Promise<void> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      await window.electronAPI.configStore.backupDatabase(backupPath);
    } else {
      console.warn('Database backup not available in browser environment');
    }
  }

  public async vacuumDatabase(): Promise<void> {
    await this.initialize();

    if (!this.initialized) {
      throw new Error('Config store not initialized');
    }

    if (typeof window !== 'undefined' && window.electronAPI?.configStore) {
      await window.electronAPI.configStore.vacuumDatabase();
    } else {
      console.warn('Database vacuum not available in browser environment');
    }
  }

  public getConfigStore(): ConfigStoreBrowser {
    return this.configStore;
  }

  public isInitialized(): boolean {
    return this.initialized;
  }
}

// Export singleton instance
export const settingsManager = new SettingsManager();