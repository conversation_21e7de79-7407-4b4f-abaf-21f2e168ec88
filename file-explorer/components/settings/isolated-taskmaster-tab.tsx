// components/settings/isolated-taskmaster-tab.tsx
import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TaskmasterSettings } from './settings-manager';
import { CheckCircle, XCircle, Loader2, Settings, Eye, EyeOff } from 'lucide-react';

interface IsolatedTaskmasterTabProps {
  settings: TaskmasterSettings;
  updateTaskmasterSettings: (updates: Partial<TaskmasterSettings>) => void;
}

/**
 * ✅ Isolated Taskmaster Tab Component
 * Uses local state for immediate UI feedback
 * Follows the same pattern as other settings tabs
 */
export const IsolatedTaskmasterTab = React.memo<IsolatedTaskmasterTabProps>(({
  settings,
  updateTaskmasterSettings
}) => {
  // ✅ Local state for immediate UI feedback
  const [localSettings, setLocalSettings] = useState<TaskmasterSettings>(settings);
  const [testingBinary, setTestingBinary] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  useEffect(() => {
    console.log('🔄 IsolatedTaskmasterTab rendered');
  });

  // ✅ Sync local state when global settings change
  useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  // ✅ Update handlers with immediate local state update
  const handleInputChange = useCallback((key: keyof TaskmasterSettings, value: string) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    updateTaskmasterSettings({ [key]: value });
  }, [localSettings, updateTaskmasterSettings]);

  const handleToggle = useCallback((key: keyof TaskmasterSettings) => {
    const newValue = !localSettings[key];
    const newSettings = { ...localSettings, [key]: newValue };
    setLocalSettings(newSettings);
    updateTaskmasterSettings({ [key]: newValue });
  }, [localSettings, updateTaskmasterSettings]);

  const handleSliderChange = useCallback((key: keyof TaskmasterSettings, value: number[]) => {
    const newValue = value[0];
    const newSettings = { ...localSettings, [key]: newValue };
    setLocalSettings(newSettings);
    updateTaskmasterSettings({ [key]: newValue });
  }, [localSettings, updateTaskmasterSettings]);

  const handleSelectChange = useCallback((key: keyof TaskmasterSettings, value: string) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    updateTaskmasterSettings({ [key]: value });
  }, [localSettings, updateTaskmasterSettings]);

  // ✅ Test Taskmaster binary availability
  const testTaskmasterBinary = useCallback(async () => {
    setTestingBinary(true);
    setTestResult(null);

    try {
      if (typeof window !== 'undefined' && window.electronAPI) {
        const command = `${localSettings.taskmasterBinaryPath} --version`;
        const result = await window.electronAPI.executeCommand(command, process.cwd());
        
        if (result.success) {
          setTestResult({
            success: true,
            message: `Taskmaster CLI found and working. Version: ${result.output?.trim() || 'Unknown'}`
          });
        } else {
          setTestResult({
            success: false,
            message: `Failed to execute Taskmaster CLI: ${result.error || 'Unknown error'}`
          });
        }
      } else {
        setTestResult({
          success: false,
          message: 'Electron API not available - cannot test CLI'
        });
      }
    } catch (error) {
      setTestResult({
        success: false,
        message: `Error testing Taskmaster CLI: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setTestingBinary(false);
    }
  }, [localSettings.taskmasterBinaryPath]);

  // ✅ Auto-detect Taskmaster binary
  const autoDetectBinary = useCallback(async () => {
    const candidates = [
      'npx task-master',
      'task-master',
      'taskmaster',
      './node_modules/.bin/task-master'
    ];

    for (const candidate of candidates) {
      try {
        if (typeof window !== 'undefined' && window.electronAPI) {
          const result = await window.electronAPI.executeCommand(`${candidate} --version`, process.cwd());
          if (result.success) {
            handleInputChange('taskmasterBinaryPath', candidate);
            setTestResult({
              success: true,
              message: `Auto-detected Taskmaster at: ${candidate}`
            });
            return;
          }
        }
      } catch (error) {
        // Continue to next candidate
      }
    }

    setTestResult({
      success: false,
      message: 'Could not auto-detect Taskmaster CLI. Please install task-master or specify custom path.'
    });
  }, [handleInputChange]);

  // ✅ Memoized input component
  const TaskmasterInput = React.memo(({
    label,
    settingKey,
    placeholder
  }: {
    label: string;
    settingKey: keyof TaskmasterSettings;
    placeholder?: string;
  }) => (
    <div className="space-y-2">
      <Label>{label}</Label>
      <Input
        value={localSettings[settingKey] as string}
        onChange={(e) => handleInputChange(settingKey, e.target.value)}
        placeholder={placeholder}
      />
    </div>
  ));

  // ✅ Memoized toggle component
  const TaskmasterToggle = React.memo(({
    id,
    label,
    settingKey,
    description
  }: {
    id: string;
    label: string;
    settingKey: keyof TaskmasterSettings;
    description?: string;
  }) => (
    <div className="flex items-center justify-between">
      <div className="space-y-0.5">
        <Label htmlFor={id}>{label}</Label>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
      <Switch
        id={id}
        checked={localSettings[settingKey] as boolean}
        onCheckedChange={() => handleToggle(settingKey)}
      />
    </div>
  ));

  // ✅ Memoized slider component
  const TaskmasterSlider = React.memo(({
    label,
    settingKey,
    min,
    max,
    step = 1
  }: {
    label: string;
    settingKey: keyof TaskmasterSettings;
    min: number;
    max: number;
    step?: number;
  }) => (
    <div className="space-y-2">
      <div className="flex justify-between">
        <Label>{label}</Label>
        <span className="text-sm text-muted-foreground">
          {localSettings[settingKey] as number}
        </span>
      </div>
      <Slider
        value={[localSettings[settingKey] as number]}
        onValueChange={(value) => handleSliderChange(settingKey, value)}
        min={min}
        max={max}
        step={step}
        className="w-full"
      />
    </div>
  ));

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Taskmaster Configuration
          </CardTitle>
          <CardDescription>Configure Taskmaster CLI integration and behavior</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* CLI Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">CLI Configuration</h4>
            
            <div className="grid grid-cols-1 gap-4">
              <TaskmasterInput
                label="Taskmaster Binary Path"
                settingKey="taskmasterBinaryPath"
                placeholder="npx task-master"
              />
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={testTaskmasterBinary}
                  disabled={testingBinary}
                >
                  {testingBinary ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Testing...
                    </>
                  ) : (
                    'Test Taskmaster Path'
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={autoDetectBinary}
                >
                  Auto-Detect
                </Button>
              </div>

              {testResult && (
                <div className={`flex items-center gap-2 p-3 rounded-md text-sm ${
                  testResult.success 
                    ? 'bg-green-50 text-green-700 border border-green-200' 
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {testResult.success ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  {testResult.message}
                </div>
              )}
            </div>

            <TaskmasterInput
              label="Default Parse Command"
              settingKey="defaultParseCommand"
              placeholder="npx task-master parse-prd"
            />
          </div>

          {/* API Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">API Configuration</h4>
            <p className="text-sm text-muted-foreground">
              Configure Taskmaster-specific API provider and key. Leave empty to inherit from global settings.
            </p>

            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label>Taskmaster API Provider</Label>
                <Select
                  value={localSettings.provider || 'global'}
                  onValueChange={(value) => handleSelectChange('provider', value === 'global' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Use global settings" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="global">Use global settings</SelectItem>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="deepseek">DeepSeek</SelectItem>
                    <SelectItem value="fireworks">Fireworks AI</SelectItem>
                    <SelectItem value="openrouter">OpenRouter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Taskmaster API Key</Label>
                <div className="flex gap-2">
                  <Input
                    type={showApiKey ? 'text' : 'password'}
                    value={localSettings.apiKey || ''}
                    onChange={(e) => handleInputChange('apiKey', e.target.value)}
                    placeholder="Enter API key for Taskmaster"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="h-10 w-10 p-0"
                  >
                    {showApiKey ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  This key will be used specifically for Taskmaster operations. If empty, global API keys will be used.
                </p>
              </div>
            </div>
          </div>

          {/* Task Configuration */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Task Configuration</h4>
            
            <div className="grid grid-cols-1 gap-4">
              <TaskmasterSlider
                label="Maximum Tasks"
                settingKey="maxTasks"
                min={10}
                max={200}
                step={5}
              />

              <TaskmasterSlider
                label="Complexity Threshold"
                settingKey="complexityThreshold"
                min={1}
                max={10}
              />
            </div>
          </div>

          {/* Behavior Settings */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium">Behavior Settings</h4>
            
            <div className="space-y-4">
              <TaskmasterToggle
                id="auto-expand-tasks"
                label="Auto-Expand Tasks"
                settingKey="autoExpandTasks"
                description="Automatically expand complex tasks into subtasks"
              />

              <TaskmasterToggle
                id="enable-verbose-logs"
                label="Enable Verbose Logs"
                settingKey="enableVerboseLogs"
                description="Show detailed logging output from Taskmaster CLI"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
});

IsolatedTaskmasterTab.displayName = 'IsolatedTaskmasterTab';

export default IsolatedTaskmasterTab;
