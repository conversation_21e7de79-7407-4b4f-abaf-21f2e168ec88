// components/settings/SettingsCenter.tsx
"use client"

import React, { useState, Suspense, lazy } from 'react';
import { SettingsManager } from './settings-manager';
import { CompleteAgentManager } from '../agents/agent-manager-complete';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Monitor, 
  Bot, 
  Key, 
  DollarSign, 
  Shield, 
  Code, 
  Terminal, 
  Brain,
  TestTube,
  Search
} from 'lucide-react';
import { cn } from '@/lib/utils';

// ✅ Lazy load settings panels for performance
const SystemSettingsPanel = lazy(() => import('./panels/SystemSettingsPanel'));
const AgentSettingsPanel = lazy(() => import('./panels/AgentSettingsPanel'));
const APIKeySettingsPanel = lazy(() => import('./panels/APIKeySettingsPanel'));
const CostSettingsPanel = lazy(() => import('./panels/CostSettingsPanel'));
const PrivacySettingsPanel = lazy(() => import('./panels/PrivacySettingsPanel'));
const EditorSettingsPanel = lazy(() => import('./panels/EditorSettingsPanel'));
const TerminalSettingsPanel = lazy(() => import('./panels/TerminalSettingsPanel'));
const TaskmasterSettingsPanel = lazy(() => import('./panels/TaskmasterSettingsPanel'));
const TestingSettingsPanel = lazy(() => import('./panels/TestingSettingsPanel'));

interface SettingsCenterProps {
  settingsManager: SettingsManager;
  agentManager?: CompleteAgentManager;
  onClose: () => void;
}

interface SettingsCategory {
  id: string;
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: React.ComponentType<any>;
  badge?: string;
  devOnly?: boolean;
}

const LoadingSpinner = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
);

export const SettingsCenter: React.FC<SettingsCenterProps> = ({ 
  settingsManager, 
  agentManager, 
  onClose 
}) => {
  const [activeCategory, setActiveCategory] = useState('system');
  const [searchQuery, setSearchQuery] = useState('');

  // ✅ Settings categories configuration
  const categories: SettingsCategory[] = [
    {
      id: 'system',
      title: 'System',
      icon: Monitor,
      description: 'Theme, auto-save, and system preferences',
      component: SystemSettingsPanel
    },
    {
      id: 'agents',
      title: 'Agents',
      icon: Bot,
      description: 'AI agent configuration and models',
      component: AgentSettingsPanel
    },
    {
      id: 'api-keys',
      title: 'API Keys',
      icon: Key,
      description: 'Manage API keys for AI providers',
      component: APIKeySettingsPanel
    },
    {
      id: 'cost',
      title: 'Cost Management',
      icon: DollarSign,
      description: 'Budget limits and cost tracking',
      component: CostSettingsPanel
    },
    {
      id: 'privacy',
      title: 'Privacy',
      icon: Shield,
      description: 'Data sharing and privacy controls',
      component: PrivacySettingsPanel
    },
    {
      id: 'editor',
      title: 'Editor',
      icon: Code,
      description: 'Code editor preferences and behavior',
      component: EditorSettingsPanel
    },
    {
      id: 'terminal',
      title: 'Terminal',
      icon: Terminal,
      description: 'Terminal appearance and behavior',
      component: TerminalSettingsPanel
    },
    {
      id: 'taskmaster',
      title: 'Taskmaster',
      icon: Brain,
      description: 'AI task orchestration settings',
      component: TaskmasterSettingsPanel,
      badge: 'AI'
    },
    {
      id: 'testing',
      title: 'Testing',
      icon: TestTube,
      description: 'Development and testing tools',
      component: TestingSettingsPanel,
      badge: 'DEV',
      devOnly: true
    }
  ];

  // ✅ Filter categories based on search and dev mode
  const filteredCategories = categories.filter(category => {
    // Hide dev-only categories in production
    if (category.devOnly && process.env.NODE_ENV === 'production') {
      return false;
    }
    
    // Hide testing category if no agentManager
    if (category.id === 'testing' && !agentManager) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      return category.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
             category.description.toLowerCase().includes(searchQuery.toLowerCase());
    }

    return true;
  });

  const activePanel = categories.find(cat => cat.id === activeCategory);

  return (
    <div className="flex h-full bg-background">
      {/* ✅ Sidebar Navigation */}
      <div className="w-80 border-r bg-muted/30">
        <div className="p-4 border-b">
          <div className="flex items-center gap-2 mb-4">
            <Settings className="h-5 w-5" />
            <h1 className="text-lg font-semibold">Settings</h1>
          </div>
          
          {/* ✅ Search Input (stubbed for now) */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search settings..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
        </div>

        {/* ✅ Category List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {filteredCategories.map((category) => {
              const Icon = category.icon;
              const isActive = activeCategory === category.id;
              
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={cn(
                    "w-full text-left p-3 rounded-lg transition-colors mb-1",
                    "hover:bg-accent hover:text-accent-foreground",
                    isActive && "bg-accent text-accent-foreground"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">{category.title}</span>
                        {category.badge && (
                          <Badge variant="secondary" className="text-xs">
                            {category.badge}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground truncate">
                        {category.description}
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </ScrollArea>
      </div>

      {/* ✅ Settings Panel Content */}
      <div className="flex-1 flex flex-col">
        {/* ✅ Panel Header */}
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {activePanel && <activePanel.icon className="h-5 w-5" />}
              <div>
                <h2 className="text-xl font-semibold">{activePanel?.title}</h2>
                <p className="text-sm text-muted-foreground">{activePanel?.description}</p>
              </div>
            </div>
          </div>
        </div>

        {/* ✅ Panel Content */}
        <div className="flex-1 overflow-auto">
          <Suspense fallback={<LoadingSpinner />}>
            {activePanel && (
              <activePanel.component
                settingsManager={settingsManager}
                agentManager={agentManager}
                onClose={onClose}
              />
            )}
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default SettingsCenter;
